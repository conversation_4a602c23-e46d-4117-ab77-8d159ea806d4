<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b0fece43-2955-4143-88c9-e6d9901e67e3" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/noval/src/main/java/org/ruoyi/system/controller/CommentGoodbadController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/noval/src/main/java/org/ruoyi/system/controller/Live2dController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/noval/src/main/java/org/ruoyi/system/domain/Mynoval.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/org/ruoyi/RuoYiAIApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/org/ruoyi/RuoYiAIApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mcp-server.json" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mcp-server.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-chat/src/main/java/org/ruoyi/common/chat/openai/OpenAiClient.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-chat/src/main/java/org/ruoyi/common/chat/openai/OpenAiClient.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-chat/src/main/java/org/ruoyi/common/chat/openai/OpenAiStreamClient.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-chat/src/main/java/org/ruoyi/common/chat/openai/OpenAiStreamClient.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-core/src/main/java/org/ruoyi/common/core/constant/CacheNames.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-core/src/main/java/org/ruoyi/common/core/constant/CacheNames.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/config/RedisConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/config/RedisConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/config/properties/RedissonProperties.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/config/properties/RedissonProperties.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/handler/KeyPrefixHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/handler/KeyPrefixHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/manager/PlusSpringCacheManager.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/manager/PlusSpringCacheManager.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/utils/CacheUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/utils/CacheUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/utils/QueueUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/utils/QueueUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/utils/RedisUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/ruoyi-common-redis/src/main/java/org/ruoyi/common/redis/utils/RedisUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-chat-api/src/main/java/org/ruoyi/service/impl/ChatSessionServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-chat-api/src/main/java/org/ruoyi/service/impl/ChatSessionServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/mapper/SysOssConfigMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/mapper/SysOssConfigMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/mapper/SysOssMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/mapper/SysOssMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/mapper/SysUserMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/mapper/SysUserMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/ISysMenuService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/ISysMenuService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/ISysOssConfigService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/ISysOssConfigService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/impl/SysMenuServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/impl/SysMenuServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/impl/SysOssServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/impl/SysOssServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/impl/SysRoleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/impl/SysRoleServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/impl/SysUserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules-api/ruoyi-system-api/src/main/java/org/ruoyi/system/service/impl/SysUserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-chat/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-chat/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-chat/src/main/java/org/ruoyi/chat/config/ChatConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-chat/src/main/java/org/ruoyi/chat/config/ChatConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-chat/src/main/java/org/ruoyi/chat/service/chat/impl/FastGPTServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-chat/src/main/java/org/ruoyi/chat/service/chat/impl/FastGPTServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-chat/src/main/java/org/ruoyi/chat/service/chat/impl/SseServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-chat/src/main/java/org/ruoyi/chat/service/chat/impl/SseServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/ruoyi/system/controller/system/SysMenuController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/ruoyi/system/controller/system/SysMenuController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/ruoyi/system/controller/system/SysNoticeController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/ruoyi/system/controller/system/SysNoticeController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/ruoyi/system/controller/system/SysOssConfigController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/ruoyi/system/controller/system/SysOssConfigController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/ruoyi/system/controller/system/SysUserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/ruoyi/system/controller/system/SysUserController.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\JavaSoft\m2\repository" />
        <option name="mavenHome" value="$PROJECT_DIR$/../../../JavaSoft/apache-maven-3.9.6" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="D:\JavaSoft\apache-maven-3.9.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectId" id="2yLSF8hVjOfE9u2656Hxuyvxom3" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Downloads/hongmeng/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;f0155382439d4d9b8f79935a379ee961&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="org.ruoyi.system.service.impl" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\daima\new\ruoyi-ai\noval\src\main" />
      <recent name="D:\daima\new\ruoyi-ai\noval\src" />
      <recent name="D:\daima\new\ruoyi-ai\ruoyi-modules\ruoyi-generator\src\main\resources\vm" />
      <recent name="D:\daima\new\ruoyi-ai\ruoyi-admin" />
      <recent name="D:\daima\new\ruoyi-ai" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\daima\new\ruoyi-ai\noval\src\main\resources\config" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="org.ruoyi.system.service.impl" />
      <recent name="org.ruoyi.system.domain" />
      <recent name="org.ruoyi.system.domain.bo" />
      <recent name="org.ruoyi.generator.util" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.RuoYiAIApplication">
    <configuration name="RuoYiAIApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-admin" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.ruoyi.RuoYiAIApplication" />
      <option name="VM_PARAMETERS" value="-Xss256k" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoyiMcpServeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-mcp-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.ruoyi.mcpserve.RuoyiMcpServeApplication" />
      <option name="VM_PARAMETERS" value="-Xss256k" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-image" temporary="true">
      <deployment type="docker-image">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="es" type="docker-deploy" factoryName="docker-image" temporary="true" server-name="Docker">
      <deployment type="docker-image">
        <settings>
          <option name="imageTag" value="elasticsearch:8.18.0" />
          <option name="containerName" value="es" />
          <option name="envVars">
            <list>
              <DockerEnvVarImpl>
                <option name="name" value="discovery.type" />
                <option name="value" value="single-node" />
              </DockerEnvVarImpl>
              <DockerEnvVarImpl>
                <option name="name" value="ES_JAVA_OPTS" />
                <option name="value" value="-Xms512m -Xmx512m" />
              </DockerEnvVarImpl>
              <DockerEnvVarImpl>
                <option name="name" value="xpack.security.enabled" />
                <option name="value" value="false" />
              </DockerEnvVarImpl>
            </list>
          </option>
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="9200" />
                <option name="hostPort" value="9200" />
              </DockerPortBindingImpl>
              <DockerPortBindingImpl>
                <option name="containerPort" value="9300" />
                <option name="hostPort" value="9300" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="commandLineOptions" value="--privileged --restart=always --network=host" />
          <option name="volumeBindings">
            <list>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/usr/share/elasticsearch/data" />
                <option name="hostPath" value="es-data" />
              </DockerVolumeBindingImpl>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/usr/share/elasticsearch/plugins" />
                <option name="hostPath" value="es-plugins" />
              </DockerVolumeBindingImpl>
            </list>
          </option>
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="kibana" type="docker-deploy" factoryName="docker-image" temporary="true" server-name="Docker">
      <deployment type="docker-image">
        <settings>
          <option name="imageTag" value="kibana:8.18.0" />
          <option name="containerName" value="kibana" />
          <option name="envVars">
            <list>
              <DockerEnvVarImpl>
                <option name="name" value="ELASTICSEARCH_HOSTS" />
                <option name="value" value="http://localhost:9200" />
              </DockerEnvVarImpl>
            </list>
          </option>
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="5601" />
                <option name="hostPort" value="5601" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="commandLineOptions" value="--restart=always --network=host" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="nginx_ruoyi_admin" type="docker-deploy" factoryName="docker-image" server-name="Docker">
      <deployment type="docker-image">
        <settings>
          <option name="imageTag" value="nginx:latest" />
          <option name="containerName" value="nginx_ruoyi_admin" />
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="80" />
                <option name="hostPort" value="80" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="commandLineOptions" value=" --network host" />
          <option name="volumeBindings">
            <list>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/var/www/html" />
                <option name="hostPath" value="/root/project/ruoyi" />
              </DockerVolumeBindingImpl>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/root/nginx_conf" />
                <option name="hostPath" value="/root/nginx/ruoyi/conf" />
              </DockerVolumeBindingImpl>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/var/log/nginx" />
                <option name="hostPath" value="/root/nginx/ruoyi/logs" />
              </DockerVolumeBindingImpl>
            </list>
          </option>
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="redis" type="docker-deploy" factoryName="docker-image" server-name="Docker">
      <deployment type="docker-image">
        <settings>
          <option name="imageTag" value="redis:latest" />
          <option name="command" value=" redis-server /etc/redis/redis.conf" />
          <option name="containerName" value="redis" />
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="6379" />
                <option name="hostPort" value="6379" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="commandLineOptions" value=" --network host" />
          <option name="volumeBindings">
            <list>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/data" />
                <option name="hostPath" value="/home/<USER>/data" />
              </DockerVolumeBindingImpl>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/etc/redis/redis.conf " />
                <option name="hostPath" value="/home/<USER>/conf/redis.conf" />
              </DockerVolumeBindingImpl>
            </list>
          </option>
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="rmqbroker" type="docker-deploy" factoryName="docker-image" temporary="true" server-name="Docker">
      <deployment type="docker-image">
        <settings>
          <option name="imageTag" value="apache/rocketmq:5.1.0" />
          <option name="command" value="sh mqbroker -c /home/<USER>/broker.conf" />
          <option name="containerName" value="rmqbroker" />
          <option name="envVars">
            <list>
              <DockerEnvVarImpl>
                <option name="name" value="MAX_HEAP_SIZE" />
                <option name="value" value="512M" />
              </DockerEnvVarImpl>
              <DockerEnvVarImpl>
                <option name="name" value="HEAP_NEWSIZE" />
                <option name="value" value="256M" />
              </DockerEnvVarImpl>
            </list>
          </option>
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="10911" />
                <option name="hostPort" value="10911" />
              </DockerPortBindingImpl>
              <DockerPortBindingImpl>
                <option name="containerPort" value="10909" />
                <option name="hostPort" value="10909" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="commandLineOptions" value="&#9;--restart=always --privileged=true -d" />
          <option name="volumeBindings">
            <list>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/root/logs" />
                <option name="hostPath" value="/usr/local/rocketmq/broker/logs" />
              </DockerVolumeBindingImpl>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/root/store" />
                <option name="hostPath" value="/usr/local/rocketmq/broker/store" />
              </DockerVolumeBindingImpl>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/home/<USER>/broker.conf" />
                <option name="hostPath" value="/usr/local/rocketmq/broker/conf/broker.conf" />
              </DockerVolumeBindingImpl>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/home/<USER>/rocketmq-5.1.0/bin/runbroker.sh" />
                <option name="hostPath" value="/usr/local/rocketmq/broker/bin/runbroker.sh" />
              </DockerVolumeBindingImpl>
            </list>
          </option>
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="rmqdashboard" type="docker-deploy" factoryName="docker-image" temporary="true" server-name="Docker">
      <deployment type="docker-image">
        <settings>
          <option name="imageTag" value="apacherocketmq/rocketmq-dashboard:latest" />
          <option name="containerName" value="rmqdashboard" />
          <option name="envVars">
            <list>
              <DockerEnvVarImpl>
                <option name="name" value="JAVA_OPTS" />
                <option name="value" value="-Xmx256M -Xms256M -Xmn128M -Drocketmq.namesrv.addr=***************:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false" />
              </DockerEnvVarImpl>
            </list>
          </option>
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="8080" />
                <option name="hostPort" value="8080" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="commandLineOptions" value="--restart=always" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="rmqnamesrv" type="docker-deploy" factoryName="docker-image" temporary="true" server-name="Docker">
      <deployment type="docker-image">
        <settings>
          <option name="imageTag" value="apache/rocketmq:5.1.0" />
          <option name="command" value="sh mqnamesrv" />
          <option name="containerName" value="rmqnamesrv" />
          <option name="entrypoint" value="" />
          <option name="envVars">
            <list>
              <DockerEnvVarImpl>
                <option name="name" value="MAX_HEAP_SIZE" />
                <option name="value" value="256M" />
              </DockerEnvVarImpl>
              <DockerEnvVarImpl>
                <option name="name" value="HEAP_NEWSIZE" />
                <option name="value" value="128M" />
              </DockerEnvVarImpl>
            </list>
          </option>
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="9876" />
                <option name="hostPort" value="9876" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="commandLineOptions" value="--privileged=true --restart=always" />
          <option name="volumeBindings">
            <list>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/home/<USER>/logs" />
                <option name="hostPath" value="/usr/local/rocketmq/nameserver/logs" />
              </DockerVolumeBindingImpl>
              <DockerVolumeBindingImpl>
                <option name="containerPath" value="/home/<USER>/rocketmq-5.1.0/bin/runserver.sh" />
                <option name="hostPath" value="/usr/local/rocketmq/nameserver/bin/runserver.sh" />
              </DockerVolumeBindingImpl>
            </list>
          </option>
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="ruoyi_admin" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="ruoyi_admin" />
          <option name="containerName" value="ruoyi_admin" />
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="6039" />
                <option name="hostPort" value="6039" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="commandLineOptions" value=" --network host" />
          <option name="sourceFilePath" value="ruoyi-admin/Dockerfile" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Docker.nginx_ruoyi_admin" />
      <item itemvalue="Docker.redis" />
      <item itemvalue="Docker.ruoyi_admin" />
      <item itemvalue="Docker.es" />
      <item itemvalue="Docker.kibana" />
      <item itemvalue="Docker.rmqbroker" />
      <item itemvalue="Docker.rmqdashboard" />
      <item itemvalue="Docker.rmqnamesrv" />
      <item itemvalue="Spring Boot.RuoYiAIApplication" />
      <item itemvalue="Spring Boot.RuoyiMcpServeApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Docker.kibana" />
        <item itemvalue="Docker.es" />
        <item itemvalue="Docker.rmqdashboard" />
        <item itemvalue="Docker.rmqbroker" />
        <item itemvalue="Docker.rmqnamesrv" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="b0fece43-2955-4143-88c9-e6d9901e67e3" name="更改" comment="" />
      <created>1749612255508</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749612255508</updated>
      <workItem from="1749612256660" duration="378000" />
      <workItem from="1749612643255" duration="1365000" />
      <workItem from="1749622123690" duration="957000" />
      <workItem from="1749623096742" duration="5064000" />
      <workItem from="1749688168315" duration="9310000" />
      <workItem from="1749721597372" duration="1190000" />
      <workItem from="1749722800818" duration="10183000" />
      <workItem from="1749775448412" duration="6637000" />
      <workItem from="1749796119385" duration="2760000" />
      <workItem from="1750034318167" duration="14501000" />
      <workItem from="1750072625273" duration="2933000" />
      <workItem from="1750121517245" duration="14068000" />
      <workItem from="1750207121608" duration="2978000" />
      <workItem from="1750238663562" duration="3747000" />
      <workItem from="1750294123283" duration="11577000" />
      <workItem from="1750332798714" duration="457000" />
      <workItem from="1750380259366" duration="9580000" />
      <workItem from="1750404806755" duration="5192000" />
      <workItem from="1750414216919" duration="4949000" />
      <workItem from="1750419507097" duration="2767000" />
      <workItem from="1750422296932" duration="168000" />
      <workItem from="1750639658258" duration="13634000" />
      <workItem from="1750726395590" duration="19205000" />
      <workItem from="1750811671885" duration="5308000" />
      <workItem from="1751248544312" duration="9924000" />
      <workItem from="1751335998123" duration="4047000" />
      <workItem from="1751351306694" duration="6631000" />
      <workItem from="1751370101083" duration="15374000" />
      <workItem from="1751416933614" duration="16717000" />
      <workItem from="1751460150940" duration="6155000" />
      <workItem from="1751471200397" duration="4000" />
      <workItem from="1751503245560" duration="1187000" />
      <workItem from="1751590204280" duration="7218000" />
      <workItem from="1751620293452" duration="4724000" />
      <workItem from="1751715773361" duration="636000" />
      <workItem from="1751850252643" duration="8351000" />
      <workItem from="1751879977701" duration="5450000" />
      <workItem from="1751935271632" duration="9755000" />
      <workItem from="1752021510568" duration="16711000" />
      <workItem from="1752051658985" duration="1424000" />
      <workItem from="1752070006794" duration="1000" />
      <workItem from="1752107885615" duration="4457000" />
      <workItem from="1752453698665" duration="18782000" />
      <workItem from="1752565977564" duration="4315000" />
      <workItem from="1752571434127" duration="192000" />
      <workItem from="1752574377929" duration="1088000" />
      <workItem from="1752626395731" duration="108000" />
      <workItem from="1752626519824" duration="18472000" />
      <workItem from="1752657352750" duration="9245000" />
      <workItem from="1752712470542" duration="11763000" />
      <workItem from="1752738677349" duration="219000" />
      <workItem from="1752738907440" duration="4103000" />
      <workItem from="1752744977881" duration="5768000" />
      <workItem from="1752800697139" duration="15153000" />
      <workItem from="1752829047273" duration="13217000" />
      <workItem from="1753060203079" duration="8600000" />
      <workItem from="1753089099310" duration="13523000" />
      <workItem from="1753145883526" duration="7157000" />
      <workItem from="1753231193448" duration="15128000" />
      <workItem from="1753319257331" duration="10931000" />
      <workItem from="1753404196747" duration="7345000" />
      <workItem from="1753663553035" duration="7350000" />
      <workItem from="1753683463733" duration="6355000" />
      <workItem from="1753752023728" duration="10893000" />
      <workItem from="1753783061862" duration="6513000" />
      <workItem from="1753836186725" duration="2581000" />
      <workItem from="1753839820038" duration="13189000" />
      <workItem from="1753922430529" duration="3193000" />
      <workItem from="1754010281158" duration="5509000" />
      <workItem from="1754132921569" duration="693000" />
      <workItem from="1754139451412" duration="1019000" />
      <workItem from="1754267994927" duration="1246000" />
      <workItem from="1754318679005" duration="7000" />
      <workItem from="1754354646045" duration="4494000" />
      <workItem from="1754374615212" duration="1420000" />
      <workItem from="1754527127664" duration="10051000" />
      <workItem from="1754547466206" duration="1049000" />
      <workItem from="1754552220766" duration="32000" />
      <workItem from="1754613975591" duration="10061000" />
      <workItem from="1754711964840" duration="11575000" />
      <workItem from="1754724791715" duration="6000" />
      <workItem from="1754726171354" duration="74000" />
      <workItem from="1754874822947" duration="1924000" />
      <workItem from="1754876762625" duration="231000" />
      <workItem from="1754877003885" duration="2511000" />
      <workItem from="1754880281735" duration="9328000" />
      <workItem from="1754918147925" duration="993000" />
      <workItem from="1754959328681" duration="6684000" />
      <workItem from="1755396918724" duration="6596000" />
      <workItem from="1755676851293" duration="1838000" />
      <workItem from="1755781409018" duration="7000" />
      <workItem from="1755783035152" duration="7000" />
      <workItem from="1755783893256" duration="523000" />
      <workItem from="1755784459987" duration="2000" />
      <workItem from="1755784474615" duration="2727000" />
      <workItem from="1755854945694" duration="505000" />
      <workItem from="1755855466426" duration="4652000" />
      <workItem from="1756018253430" duration="951000" />
      <workItem from="1756020502715" duration="5443000" />
      <workItem from="1756189146693" duration="4000" />
      <workItem from="1756189174437" duration="28510000" />
      <workItem from="1756272098762" duration="600000" />
      <workItem from="1756273799997" duration="6329000" />
      <workItem from="1756478129695" duration="599000" />
      <workItem from="1756633129445" duration="599000" />
      <workItem from="1756638896016" duration="599000" />
      <workItem from="1756646844570" duration="599000" />
      <workItem from="1756954931228" duration="7000" />
      <workItem from="1756972842155" duration="8000" />
      <workItem from="1757228063963" duration="599000" />
      <workItem from="1757252782513" duration="599000" />
      <workItem from="1757293334087" duration="599000" />
      <workItem from="1757321822935" duration="11000" />
      <workItem from="1757379917584" duration="88000" />
      <workItem from="1757398273601" duration="10045000" />
      <workItem from="1757431148473" duration="4000" />
      <workItem from="1757465700940" duration="17402000" />
      <workItem from="1757562948338" duration="599000" />
      <workItem from="1757641975706" duration="2488000" />
      <workItem from="1757654252957" duration="1414000" />
      <workItem from="1757658605370" duration="7135000" />
      <workItem from="1757758362254" duration="599000" />
      <workItem from="1757766278866" duration="599000" />
      <workItem from="1757829628111" duration="599000" />
      <workItem from="1757896330413" duration="702000" />
      <workItem from="1757897987964" duration="11020000" />
      <workItem from="1757917672300" duration="8640000" />
      <workItem from="1757984574809" duration="639000" />
      <workItem from="1757985282213" duration="8099000" />
      <workItem from="1757998091907" duration="4652000" />
      <workItem from="1758071260901" duration="11627000" />
      <workItem from="1758103496562" duration="217000" />
      <workItem from="1758103735186" duration="712000" />
      <workItem from="1758171787715" duration="7872000" />
      <workItem from="1758244315014" duration="2738000" />
      <workItem from="1758257983995" duration="601000" />
      <workItem from="1758262217030" duration="4634000" />
      <workItem from="1758503301686" duration="4443000" />
      <workItem from="1758522444943" duration="2520000" />
      <workItem from="1758595300836" duration="1916000" />
      <workItem from="1758602541263" duration="2030000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="main" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>